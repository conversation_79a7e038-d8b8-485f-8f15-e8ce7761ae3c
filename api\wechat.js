/**
 * 微信相关API接口
 * 注意：这是前端示例代码，实际的签名生成需要在后端服务器完成
 */

// 微信公众号配置
const WECHAT_CONFIG = {
  appId: 'wxa97806e0ac0e9c08',
  appSecret: 'f4c257ee7c9bdddf84349b7067e59711'
}

/**
 * 获取微信Access Token
 * 注意：这个函数应该在后端实现，这里仅作为示例
 */
async function getAccessToken() {
  try {
    const response = await uni.request({
      url: `https://api.weixin.qq.com/cgi-bin/token`,
      method: 'GET',
      data: {
        grant_type: 'client_credential',
        appid: WECHAT_CONFIG.appId,
        secret: WECHAT_CONFIG.appSecret
      }
    })
    
    if (response.data && response.data.access_token) {
      return response.data.access_token
    } else {
      throw new Error('获取Access Token失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取Access Token失败:', error)
    throw error
  }
}

/**
 * 获取微信JS API Ticket
 * 注意：这个函数应该在后端实现，这里仅作为示例
 */
async function getJsApiTicket(accessToken) {
  try {
    const response = await uni.request({
      url: `https://api.weixin.qq.com/cgi-bin/ticket/getticket`,
      method: 'GET',
      data: {
        access_token: accessToken,
        type: 'jsapi'
      }
    })
    
    if (response.data && response.data.ticket) {
      return response.data.ticket
    } else {
      throw new Error('获取JS API Ticket失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取JS API Ticket失败:', error)
    throw error
  }
}

/**
 * 生成随机字符串
 */
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15)
}

/**
 * 生成时间戳
 */
function generateTimestamp() {
  return parseInt(new Date().getTime() / 1000).toString()
}

/**
 * SHA1加密
 * 注意：实际项目中建议使用crypto-js库
 */
function sha1(str) {
  // 这里需要引入SHA1加密库，或者在后端实现
  // 示例使用crypto-js库的用法：
  // import CryptoJS from 'crypto-js'
  // return CryptoJS.SHA1(str).toString()
  
  // 临时返回，实际需要真正的SHA1加密
  console.warn('需要实现SHA1加密函数')
  return str
}

/**
 * 生成微信JSSDK签名
 * 注意：这个函数应该在后端实现，避免暴露appSecret
 */
function generateSignature(ticket, nonceStr, timestamp, url) {
  const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return sha1(string1)
}

/**
 * 获取微信JSSDK配置
 * 这是一个示例实现，实际应该调用后端接口
 */
export async function getWechatJSSDKConfig(url) {
  try {
    // 实际项目中，这些步骤应该在后端完成
    // 1. 获取access_token
    const accessToken = await getAccessToken()
    
    // 2. 获取jsapi_ticket
    const ticket = await getJsApiTicket(accessToken)
    
    // 3. 生成签名所需参数
    const nonceStr = generateNonceStr()
    const timestamp = generateTimestamp()
    
    // 4. 生成签名
    const signature = generateSignature(ticket, nonceStr, timestamp, url)
    
    return {
      appId: WECHAT_CONFIG.appId,
      timestamp: timestamp,
      nonceStr: nonceStr,
      signature: signature
    }
  } catch (error) {
    console.error('获取微信JSSDK配置失败:', error)
    throw error
  }
}

/**
 * 后端API接口示例
 * 实际项目中应该创建真正的后端服务
 */
export const wechatAPI = {
  /**
   * 获取JSSDK签名配置
   */
  async getJsApiSignature(url) {
    try {
      // 这里应该调用真正的后端接口
      // const response = await uni.request({
      //   url: 'https://your-backend-api.com/api/wechat/getJsApiSignature',
      //   method: 'POST',
      //   data: { url }
      // })
      // return response.data
      
      // 临时使用前端实现（不推荐，仅用于测试）
      const config = await getWechatJSSDKConfig(url)
      console.log('获取JSSDK签名配置',config);
      
      return {
        code: 200,
        message: 'success',
        data: config
      }
    } catch (error) {
      return {
        code: 500,
        message: error.message,
        data: null
      }
    }
  }
}
