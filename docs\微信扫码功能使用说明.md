# 微信扫码功能使用说明

## 概述

本项目已集成微信公众号JSSDK扫码功能，用于实现"扫码用水"功能。用户可以在微信公众号中打开H5页面，通过扫描二维码来控制用水设备。

## 配置信息

- **AppID**: `wxa97806e0ac0e9c08`
- **AppSecret**: `f4c257ee7c9bdddf84349b7067e59711`

## 文件结构

```
├── utils/wechat.js                    # 微信JSSDK工具类
├── api/wechat.js                      # 微信API接口（前端示例）
├── docs/backend-api-example.js       # 后端API示例（Node.js）
├── pages/scanWater/index.vue          # 扫码用水页面
└── index.html                         # 已引入微信JSSDK
```

## 功能特性

### 1. 微信环境检测
- 自动检测是否在微信浏览器中运行
- 非微信环境会提示用户在微信中打开

### 2. JSSDK初始化
- 自动获取微信签名配置
- 配置必要的JS接口权限
- 错误处理和重试机制

### 3. 扫码功能
- 支持二维码和条形码扫描
- 实时返回扫描结果
- 用户友好的交互界面

### 4. 错误处理
- 完善的错误提示
- 网络异常处理
- 用户取消操作处理

## 部署步骤

### 1. 后端API部署

**重要**: 为了安全起见，微信签名必须在后端生成，不能在前端暴露AppSecret。

#### 方式一：使用提供的Node.js示例

```bash
# 1. 创建后端项目
mkdir wechat-api-server
cd wechat-api-server

# 2. 初始化项目
npm init -y

# 3. 安装依赖
npm install express crypto axios

# 4. 复制 docs/backend-api-example.js 到项目根目录
cp ../docs/backend-api-example.js ./server.js

# 5. 启动服务
node server.js
```

#### 方式二：集成到现有后端

将 `docs/backend-api-example.js` 中的代码集成到您现有的后端服务中。

### 2. 前端配置

修改 `utils/wechat.js` 中的API地址：

```javascript
// 将这行
url: '/api/wechat/getJsApiSignature',

// 修改为您的后端API地址
url: 'https://your-backend-domain.com/api/wechat/getJsApiSignature',
```

### 3. 微信公众号配置

1. 登录微信公众平台
2. 进入"设置与开发" -> "公众号设置" -> "功能设置"
3. 设置"JS接口安全域名"为您的H5页面域名
4. 确保域名已备案且可通过HTTPS访问

## 使用方法

### 1. 页面集成

在需要扫码功能的页面中引入组件：

```vue
<template>
  <view>
    <!-- 其他内容 -->
    <ScanWater />
  </view>
</template>

<script setup>
import ScanWater from '@/pages/scanWater/index.vue'
</script>
```

### 2. 直接使用工具类

```javascript
import wechatSDK from '@/utils/wechat.js'

// 初始化
await wechatSDK.init()

// 扫码
try {
  const result = await wechatSDK.scanQRCode()
  console.log('扫码结果:', result)
} catch (error) {
  console.error('扫码失败:', error)
}
```

## 测试步骤

### 1. 本地测试

1. 启动后端API服务
2. 启动前端开发服务器
3. 使用微信开发者工具打开页面
4. 测试扫码功能

### 2. 线上测试

1. 部署后端API到服务器
2. 部署前端到HTTPS域名
3. 配置微信公众号JS接口安全域名
4. 在微信中访问页面测试

## 常见问题

### 1. "config:invalid signature" 错误

- 检查JS接口安全域名配置
- 确认URL参数正确（不包含#后面的部分）
- 验证后端签名算法

### 2. "permission denied" 错误

- 检查jsApiList配置
- 确认公众号类型支持相应接口

### 3. 扫码功能无响应

- 确认在微信浏览器中打开
- 检查JSSDK初始化状态
- 查看控制台错误信息

### 4. 网络请求失败

- 检查后端API服务状态
- 确认跨域配置正确
- 验证请求URL和参数

## 安全注意事项

1. **绝不在前端暴露AppSecret**
2. **使用HTTPS协议**
3. **验证扫码结果的合法性**
4. **实现适当的用户权限验证**
5. **记录操作日志**

## 扩展功能

可以基于当前实现扩展以下功能：

1. **地理位置获取**: 验证用户是否在设备附近
2. **图片上传**: 上传用水凭证
3. **支付集成**: 集成微信支付
4. **用户信息获取**: 获取微信用户基本信息

## 技术支持

如有问题，请检查：

1. 浏览器控制台错误信息
2. 微信开发者工具调试信息
3. 后端API日志
4. 微信公众平台错误日志
