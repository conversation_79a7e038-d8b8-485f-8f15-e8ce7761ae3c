/**
 * 开发环境用的简单后端服务器
 * 用于提供微信JSSDK签名接口
 * 
 * 使用方法：
 * 1. npm install express cors crypto
 * 2. node dev-server.js
 */

const express = require('express')
const cors = require('cors')
const crypto = require('crypto')

const app = express()
const PORT = 3001

// 中间件
app.use(cors())
app.use(express.json())

// 微信配置
const WECHAT_CONFIG = {
  appId: 'wxa97806e0ac0e9c08',
  appSecret: 'f4c257ee7c9bdddf84349b7067e59711'
}

// 生成随机字符串
function generateNonceStr() {
  return Math.random().toString(36).substring(2, 15)
}

// 生成时间戳
function generateTimestamp() {
  return parseInt(new Date().getTime() / 1000).toString()
}

// SHA1加密
function sha1(str) {
  return crypto.createHash('sha1').update(str).digest('hex')
}

// 模拟获取access_token（开发环境用）
function getMockAccessToken() {
  return 'mock_access_token_' + Date.now()
}

// 模拟获取jsapi_ticket（开发环境用）
function getMockJsApiTicket() {
  return 'mock_jsapi_ticket_' + Date.now()
}

// 生成签名
function generateSignature(ticket, nonceStr, timestamp, url) {
  const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return sha1(string1)
}

// 获取微信JSSDK配置接口
app.post('/api/wechat/getJsApiSignature', (req, res) => {
  try {
    const { url } = req.body
    
    console.log('收到签名请求，URL:', url)
    
    if (!url) {
      return res.status(400).json({
        code: 400,
        message: 'URL参数不能为空',
        data: null
      })
    }
    
    // 开发环境：生成模拟签名
    const ticket = getMockJsApiTicket()
    const nonceStr = generateNonceStr()
    const timestamp = generateTimestamp()
    const signature = generateSignature(ticket, nonceStr, timestamp, url)
    
    const config = {
      appId: WECHAT_CONFIG.appId,
      timestamp: timestamp,
      nonceStr: nonceStr,
      signature: signature
    }
    
    console.log('生成的配置:', config)
    
    res.json({
      code: 200,
      message: 'success',
      data: config
    })
    
  } catch (error) {
    console.error('生成签名失败:', error)
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    })
  }
})

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: '开发服务器运行正常' })
})

app.listen(PORT, () => {
  console.log(`🚀 开发服务器启动成功！`)
  console.log(`📍 服务地址: http://localhost:${PORT}`)
  console.log(`🔧 微信签名接口: http://localhost:${PORT}/api/wechat/getJsApiSignature`)
  console.log(`💡 这是开发环境用的模拟服务器，生产环境请使用真实的后端服务`)
})

module.exports = app
