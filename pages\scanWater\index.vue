<template>
	<!-- 扫码用水 -->
	<view class="function-btn" @click="scanWater" :class="{ 'disabled': isLoading }">
		<text class="iconfont" v-if="!isLoading">&#xe600;</text>
		<text class="loading-icon" v-else>⟳</text>
		<text class="btn-text">{{ isLoading ? '处理中...' : '扫码用水' }}</text>
	</view>
</template>

<script setup>

import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import wechatSDK from '@/utils/wechat.js'

// 响应式数据
const isLoading = ref(false)
const isWechatReady = ref(false)

// 生命周期钩子
onLoad(async () => {
	// 初始化微信JSSDK
	await initWechatSDK()
})

// 初始化微信JSSDK
const initWechatSDK = async () => {
	try {
		isLoading.value = true

		// 检查是否在微信浏览器中
		if (!wechatSDK.isWechat) {
			uni.showToast({
				title: '请在微信中打开',
				icon: 'none',
				duration: 2000
			})
			return
		}

		// 初始化微信JSSDK
		const success = await wechatSDK.init()
		isWechatReady.value = success

		if (!success) {
			uni.showToast({
				title: '微信初始化失败',
				icon: 'none',
				duration: 2000
			})
		}
	} catch (error) {
		console.error('初始化微信JSSDK失败:', error)
		uni.showToast({
			title: '微信初始化失败',
			icon: 'none',
			duration: 2000
		})
	} finally {
		isLoading.value = false
	}
}

// 开始扫码
const scanWater = async () => {
	if (isLoading.value) {
		return
	}

	try {
		isLoading.value = true

		// 检查微信JSSDK是否准备就绪
		if (!isWechatReady.value) {
			uni.showToast({
				title: '微信功能未就绪，请稍后重试',
				icon: 'none',
				duration: 2000
			})
			return
		}

		// 调用微信扫码功能
		const result = await wechatSDK.scanQRCode({
			needResult: 1, // 直接返回扫描结果
			scanType: ["qrCode", "barCode"] // 支持二维码和条形码
		})

		// 处理扫码结果
		handleScanResult(result)

	} catch (error) {
		console.error('扫码失败:', error)

		let errorMessage = '扫码失败'
		if (error.message === '请在微信中打开') {
			errorMessage = '请在微信中打开此页面'
		} else if (error.message === '用户取消扫码') {
			errorMessage = '已取消扫码'
		} else if (error.message === '微信JSSDK未初始化') {
			errorMessage = '微信功能未就绪，请刷新页面重试'
		}

		uni.showToast({
			title: errorMessage,
			icon: 'none',
			duration: 2000
		})
	} finally {
		isLoading.value = false
	}
}

// 处理扫码结果
const handleScanResult = (result) => {
	console.log('扫码结果:', result)

	// 解析扫码结果
	let qrData = result
	try {
		// 如果是JSON格式，尝试解析
		if (result.startsWith('{') && result.endsWith('}')) {
			qrData = JSON.parse(result)
		}
	} catch (e) {
		// 不是JSON格式，使用原始字符串
		console.log('扫码结果不是JSON格式，使用原始字符串')
	}

	uni.showModal({
		title: '扫码成功',
		content: `扫码结果：${typeof qrData === 'object' ? JSON.stringify(qrData, null, 2) : qrData}`,
		confirmText: '确定',
		cancelText: '重新扫码',
		success: function (res) {
			if (res.confirm) {
				// 用户点击确定，进行下一步操作
				console.log('用户确认扫码结果')
				processWaterDevice(qrData)
			} else if (res.cancel) {
				// 用户点击重新扫码
				setTimeout(() => {
					scanWater()
				}, 500)
			}
		}
	})
}

// 处理用水设备
const processWaterDevice = (qrData) => {
	// 这里可以根据扫码结果进行相应的业务处理
	// 比如：设备控制、用户验证、费用扣除等
	console.log('处理用水设备，扫码数据:', qrData)

	uni.showLoading({
		title: '正在处理...'
	})

	// 模拟处理过程
	setTimeout(() => {
		uni.hideLoading()

		uni.showToast({
			title: '用水成功！',
			icon: 'success',
			duration: 2000
		})

		// 可以在这里添加更多业务逻辑
		// 比如：记录用水记录、更新用户余额等
		// 例如：调用后端API记录用水信息
		// recordWaterUsage(qrData)
	}, 2000)
}

</script>
<style lang="scss" scoped>
.function-btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 12rpx;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.3);
	}

	&.disabled {
		opacity: 0.6;
		cursor: not-allowed;
		transform: none;

		&:hover {
			transform: none;
			box-shadow: none;
		}
	}
}

.iconfont {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
}

.loading-icon {
	font-size: 44rpx;
	color: #ffffff;
	margin-right: 15rpx;
	line-height: 1;
	display: inline-block;
	width: 44rpx;
	text-align: center;
	animation: rotate 1s linear infinite;
}

.btn-text {
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 500;
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}
</style>
