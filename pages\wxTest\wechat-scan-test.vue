<template>
  <view class="container">
    <view class="header">
      <text class="title">微信扫码功能测试</text>
    </view>
    
    <view class="status-section">
      <view class="status-item">
        <text class="label">微信环境:</text>
        <text class="value" :class="{ 'success': isWechat, 'error': !isWechat }">
          {{ isWechat ? '✓ 微信浏览器' : '✗ 非微信环境' }}
        </text>
      </view>
      
      <view class="status-item">
        <text class="label">JSSDK状态:</text>
        <text class="value" :class="{ 'success': isWechatReady, 'error': !isWechatReady, 'warning': isInitializing }">
          {{ getSDKStatus() }}
        </text>
      </view>
    </view>
    
    <view class="button-section">
      <button class="test-btn" @click="initSDK" :disabled="isInitializing">
        {{ isInitializing ? '初始化中...' : '重新初始化JSSDK' }}
      </button>
      
      <button class="test-btn primary" @click="testScan" :disabled="!isWechatReady || isScanning">
        {{ isScanning ? '扫码中...' : '测试扫码功能' }}
      </button>
    </view>
    
    <view class="result-section" v-if="scanResult">
      <view class="result-title">扫码结果:</view>
      <view class="result-content">{{ scanResult }}</view>
    </view>
    
    <view class="log-section">
      <view class="log-title">调试日志:</view>
      <view class="log-content">
        <view v-for="(log, index) in logs" :key="index" class="log-item">
          <text class="log-time">{{ log.time }}</text>
          <text class="log-message" :class="log.type">{{ log.message }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import wechatSDK from '@/utils/wechat.js'

// 响应式数据
const isWechat = ref(false)
const isWechatReady = ref(false)
const isInitializing = ref(false)
const isScanning = ref(false)
const scanResult = ref('')
const logs = ref([])

// 生命周期
onMounted(() => {
  isWechat.value = wechatSDK.isWechat
  addLog('页面加载完成', 'info')
  
  if (isWechat.value) {
    initSDK()
  } else {
    addLog('当前不在微信环境中', 'warning')
  }
})

// 添加日志
const addLog = (message, type = 'info') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  logs.value.unshift({
    time,
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value.pop()
  }
}

// 获取SDK状态文本
const getSDKStatus = () => {
  if (isInitializing.value) return '⏳ 初始化中...'
  if (isWechatReady.value) return '✓ 已就绪'
  return '✗ 未初始化'
}

// 初始化SDK
const initSDK = async () => {
  if (isInitializing.value) return
  
  try {
    isInitializing.value = true
    isWechatReady.value = false
    addLog('开始初始化微信JSSDK', 'info')
    
    const success = await wechatSDK.init()
    isWechatReady.value = success
    
    if (success) {
      addLog('微信JSSDK初始化成功', 'success')
    } else {
      addLog('微信JSSDK初始化失败', 'error')
    }
  } catch (error) {
    addLog(`初始化失败: ${error.message}`, 'error')
    isWechatReady.value = false
  } finally {
    isInitializing.value = false
  }
}

// 测试扫码
const testScan = async () => {
  if (isScanning.value || !isWechatReady.value) return
  
  try {
    isScanning.value = true
    scanResult.value = ''
    addLog('开始扫码测试', 'info')
    
    const result = await wechatSDK.scanQRCode({
      needResult: 1,
      scanType: ["qrCode", "barCode"]
    })
    
    scanResult.value = result
    addLog(`扫码成功: ${result}`, 'success')
    
  } catch (error) {
    addLog(`扫码失败: ${error.message}`, 'error')
  } finally {
    isScanning.value = false
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 40rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
}

.status-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  font-size: 32rpx;
  color: #666;
}

.value {
  font-size: 32rpx;
  font-weight: 500;
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
  
  &.warning {
    color: #faad14;
  }
}

.button-section {
  margin-bottom: 40rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  margin-bottom: 20rpx;
  border: none;
  background: #f0f0f0;
  color: #666;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }
  
  &:disabled {
    opacity: 0.5;
  }
}

.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.result-content {
  font-size: 28rpx;
  color: #666;
  background: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  word-break: break-all;
}

.log-section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.log-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
}

.log-content {
  max-height: 600rpx;
  overflow-y: auto;
}

.log-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 24rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.log-time {
  color: #999;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-message {
  flex: 1;
  
  &.info {
    color: #666;
  }
  
  &.success {
    color: #52c41a;
  }
  
  &.error {
    color: #ff4d4f;
  }
  
  &.warning {
    color: #faad14;
  }
}
</style>
