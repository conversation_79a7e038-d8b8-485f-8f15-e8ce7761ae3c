# 开发环境启动说明

## 🚀 快速启动

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

**方式一：使用npm脚本**
```bash
npm run dev-server
```

**方式二：直接运行**
```bash
node dev-server.js
```

### 3. 启动前端开发服务器

```bash
# 如果使用HBuilderX，直接运行到浏览器
# 或者使用命令行
npm run dev:h5
```

## 📋 启动检查清单

### ✅ 后端服务检查

1. **服务启动成功**
   - 看到 `🚀 开发服务器启动成功！` 消息
   - 服务运行在 `http://localhost:3001`

2. **接口测试**
   ```bash
   # 健康检查
   curl http://localhost:3001/health
   
   # 签名接口测试
   curl -X POST http://localhost:3001/api/wechat/getJsApiSignature \
     -H "Content-Type: application/json" \
     -d '{"url":"http://localhost:5173"}'
   ```

### ✅ 前端服务检查

1. **前端服务启动**
   - 通常运行在 `http://localhost:5173`
   - 或者HBuilderX指定的端口

2. **页面访问**
   - 访问扫码页面：`http://localhost:5173/#/pages/scanWater/index`
   - 访问测试页面：`http://localhost:5173/#/pages/wxTest/wechat-scan-test`

## 🔧 调试步骤

### 1. 检查控制台输出

**后端控制台应该显示：**
```
🚀 开发服务器启动成功！
📍 服务地址: http://localhost:3001
🔧 微信签名接口: http://localhost:3001/api/wechat/getJsApiSignature
💡 这是开发环境用的模拟服务器，生产环境请使用真实的后端服务
```

**前端控制台应该显示：**
```
开始初始化微信JSSDK...
信息 {appId: "wxa97806e0ac0e9c08", timestamp: "...", nonceStr: "...", signature: "..."}
微信JSSDK初始化结果: true
微信JSSDK初始化成功
```

### 2. 常见问题解决

**问题1：端口冲突**
```bash
# 如果3001端口被占用，修改dev-server.js中的PORT变量
const PORT = 3002  // 改为其他端口
```

**问题2：跨域问题**
- 开发服务器已配置CORS，应该不会有跨域问题
- 如果仍有问题，检查浏览器控制台的具体错误

**问题3：依赖安装失败**
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules
npm install
```

## 📱 微信环境测试

### 1. 微信开发者工具测试

1. 打开微信开发者工具
2. 选择"公众号网页调试"
3. 输入URL：`http://localhost:5173/#/pages/scanWater/index`
4. 测试扫码功能

### 2. 手机微信测试

1. 确保手机和电脑在同一网络
2. 获取电脑IP地址（如：*************）
3. 修改前端访问地址为：`http://*************:5173`
4. 在微信中访问测试

## ⚠️ 注意事项

1. **开发环境限制**
   - 当前使用的是模拟签名，不是真实的微信签名
   - 在真实微信环境中可能无法正常工作
   - 仅用于开发调试界面和逻辑

2. **生产环境部署**
   - 生产环境必须使用真实的后端服务
   - 需要配置正确的微信公众号参数
   - 必须使用HTTPS协议

3. **安全提醒**
   - 不要在生产环境使用dev-server.js
   - AppSecret不应该暴露在前端代码中

## 🎯 下一步

1. **界面调试完成后**，部署真实的后端服务
2. **配置微信公众号**的JS接口安全域名
3. **使用HTTPS**进行生产环境测试
