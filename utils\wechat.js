/**
 * 微信JSSDK工具类
 * 用于在H5页面中调用微信扫码等功能
 */

class WechatSDK {
  constructor() {
    this.appId = 'wxa97806e0ac0e9c08'
    this.isReady = false
    this.isWechat = this.isWechatBrowser()
  }

  /**
   * 检测是否在微信浏览器中
   */
  isWechatBrowser() {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  }

  /**
   * 初始化微信JSSDK
   * @param {string} url - 当前页面URL
   */
  async init(url = window.location.href) {
    if (!this.isWechat) {
      console.warn('当前不在微信浏览器环境中')
      return false
    }

    try {
      // 获取微信签名配置
      const config = await this.getWechatConfig(url)
      console.log('信息',config);
      
      
      // 配置微信JSSDK
      wx.config({
        debug: true, // 开启调试模式，便于调试
        appId: this.appId,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.signature,
        jsApiList: [
          'scanQRCode', // 扫描二维码
          'chooseImage', // 拍照或从手机相册中选图
          'previewImage', // 预览图片
          'getLocation' // 获取地理位置
        ]
      })

      return new Promise((resolve, reject) => {
        wx.ready(() => {
          console.log('微信JSSDK初始化成功')
          this.isReady = true
          resolve(true)
        })

        wx.error((res) => {
          console.error('微信JSSDK初始化失败:', res)
          this.isReady = false
          reject(res)
        })
      })
    } catch (error) {
      console.error('获取微信配置失败:', error)
      return false
    }
  }

  /**
   * 获取微信JSSDK配置
   * @param {string} url - 当前页面URL
   */
  async getWechatConfig(url) {
    return new Promise((resolve, reject) => {
      // 这里需要调用后端接口获取签名
      // 暂时返回模拟数据，后续需要实现真实的后端接口
      uni.request({
        url: 'http://localhost:3001/api/wechat/getJsApiSignature', // 开发环境后端接口地址
        method: 'POST',
        data: {
          url: url
        },
        success: (response) => {
          if (response.data && response.data.code === 200) {
            resolve(response.data.data)
          } else {
            reject(new Error('获取微信配置失败: ' + (response.data?.message || '未知错误')))
          }
        },
        fail: (error) => {
          console.error('请求失败:', error)
          console.warn('后端接口不可用，使用前端临时实现进行测试')
          // 如果后端接口不可用，使用前端临时实现（仅用于开发测试）
          this.getWechatConfigFallback(url).then(resolve).catch(reject)
        }
      })
    })
  }

  /**
   * 临时的前端实现（仅用于开发测试，生产环境必须使用后端接口）
   */
  async getWechatConfigFallback(url) {
    console.warn('使用前端临时实现获取微信配置，生产环境请使用后端接口')
    console.log('当前URL:', url)

    // 生成临时配置（注意：这不是真正的签名，仅用于测试）
    const timestamp = Math.floor(Date.now() / 1000).toString()
    const nonceStr = Math.random().toString(36).substring(2, 15)

    return {
      appId: this.appId,
      timestamp: timestamp,
      nonceStr: nonceStr,
      signature: 'temp_signature_for_development' // 临时签名
    }
  }

  /**
   * 扫描二维码
   * @param {Object} options - 扫码配置选项
   */
  scanQRCode(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isWechat) {
        reject(new Error('请在微信中打开'))
        return
      }

      if (!this.isReady) {
        reject(new Error('微信JSSDK未初始化'))
        return
      }

      const defaultOptions = {
        needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果
        scanType: ["qrCode", "barCode"], // 可以指定扫二维码还是一维码，默认二者都有
        success: (res) => {
          // 当needResult 为 1 时，扫码返回的结果
          console.log('扫码成功:', res.resultStr)
          resolve(res.resultStr)
        },
        error: (err) => {
          console.error('扫码失败:', err)
          reject(err)
        },
        cancel: () => {
          console.log('用户取消扫码')
          reject(new Error('用户取消扫码'))
        }
      }

      const finalOptions = { ...defaultOptions, ...options }
      
      wx.scanQRCode(finalOptions)
    })
  }

  /**
   * 获取地理位置
   */
  getLocation() {
    return new Promise((resolve, reject) => {
      if (!this.isWechat) {
        reject(new Error('请在微信中打开'))
        return
      }

      if (!this.isReady) {
        reject(new Error('微信JSSDK未初始化'))
        return
      }

      wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            speed: res.speed,
            accuracy: res.accuracy
          })
        },
        error: (err) => {
          reject(err)
        }
      })
    })
  }
}

// 创建单例实例
const wechatSDK = new WechatSDK()

export default wechatSDK
