/**
 * 后端API示例 - Node.js + Express
 * 这个文件展示了如何在后端安全地实现微信JSSDK签名接口
 * 
 * 安装依赖：
 * npm install express crypto axios
 */

const express = require('express')
const crypto = require('crypto')
const axios = require('axios')

const app = express()
app.use(express.json())

// 微信公众号配置
const WECHAT_CONFIG = {
  appId: 'wxa97806e0ac0e9c08',
  appSecret: 'f4c257ee7c9bdddf84349b7067e59711'
}

// 缓存access_token和jsapi_ticket
let tokenCache = {
  access_token: null,
  access_token_expires: 0,
  jsapi_ticket: null,
  jsapi_ticket_expires: 0
}

/**
 * 获取微信Access Token
 */
async function getAccessToken() {
  // 检查缓存是否有效
  if (tokenCache.access_token && Date.now() < tokenCache.access_token_expires) {
    return tokenCache.access_token
  }

  try {
    const response = await axios.get('https://api.weixin.qq.com/cgi-bin/token', {
      params: {
        grant_type: 'client_credential',
        appid: WECHAT_CONFIG.appId,
        secret: WECHAT_CONFIG.appSecret
      }
    })

    if (response.data && response.data.access_token) {
      // 缓存token，提前5分钟过期
      tokenCache.access_token = response.data.access_token
      tokenCache.access_token_expires = Date.now() + (response.data.expires_in - 300) * 1000
      
      return response.data.access_token
    } else {
      throw new Error('获取Access Token失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取Access Token失败:', error.message)
    throw error
  }
}

/**
 * 获取微信JS API Ticket
 */
async function getJsApiTicket() {
  // 检查缓存是否有效
  if (tokenCache.jsapi_ticket && Date.now() < tokenCache.jsapi_ticket_expires) {
    return tokenCache.jsapi_ticket
  }

  try {
    const accessToken = await getAccessToken()
    const response = await axios.get('https://api.weixin.qq.com/cgi-bin/ticket/getticket', {
      params: {
        access_token: accessToken,
        type: 'jsapi'
      }
    })

    if (response.data && response.data.ticket) {
      // 缓存ticket，提前5分钟过期
      tokenCache.jsapi_ticket = response.data.ticket
      tokenCache.jsapi_ticket_expires = Date.now() + (response.data.expires_in - 300) * 1000
      
      return response.data.ticket
    } else {
      throw new Error('获取JS API Ticket失败: ' + JSON.stringify(response.data))
    }
  } catch (error) {
    console.error('获取JS API Ticket失败:', error.message)
    throw error
  }
}

/**
 * 生成随机字符串
 */
function generateNonceStr() {
  return Math.random().toString(36).substr(2, 15)
}

/**
 * 生成时间戳
 */
function generateTimestamp() {
  return parseInt(new Date().getTime() / 1000).toString()
}

/**
 * SHA1加密
 */
function sha1(str) {
  return crypto.createHash('sha1').update(str).digest('hex')
}

/**
 * 生成微信JSSDK签名
 */
function generateSignature(ticket, nonceStr, timestamp, url) {
  const string1 = `jsapi_ticket=${ticket}&noncestr=${nonceStr}&timestamp=${timestamp}&url=${url}`
  return sha1(string1)
}

/**
 * 获取微信JSSDK配置接口
 */
app.post('/api/wechat/getJsApiSignature', async (req, res) => {
  try {
    const { url } = req.body

    if (!url) {
      return res.status(400).json({
        code: 400,
        message: 'URL参数不能为空',
        data: null
      })
    }

    // 获取jsapi_ticket
    const ticket = await getJsApiTicket()
    
    // 生成签名所需参数
    const nonceStr = generateNonceStr()
    const timestamp = generateTimestamp()
    
    // 生成签名
    const signature = generateSignature(ticket, nonceStr, timestamp, url)
    
    res.json({
      code: 200,
      message: 'success',
      data: {
        appId: WECHAT_CONFIG.appId,
        timestamp: timestamp,
        nonceStr: nonceStr,
        signature: signature
      }
    })
  } catch (error) {
    console.error('获取微信JSSDK配置失败:', error)
    res.status(500).json({
      code: 500,
      message: error.message,
      data: null
    })
  }
})

// 跨域处理
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Headers', 'Content-Type')
  res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
  if (req.method === 'OPTIONS') {
    res.sendStatus(200)
  } else {
    next()
  }
})

const PORT = process.env.PORT || 3000
app.listen(PORT, () => {
  console.log(`微信JSSDK后端服务启动成功，端口: ${PORT}`)
})

module.exports = app
